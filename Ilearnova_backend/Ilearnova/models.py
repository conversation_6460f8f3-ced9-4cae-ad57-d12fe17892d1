from django.contrib.auth.models import AbstractUser
from django.db import models

class CustomUser(AbstractUser):
    email = models.EmailField(unique=True)
    bio = models.TextField(blank=True, null=True)
    profile_pic = models.ImageField(upload_to='profiles/', blank=True, null=True)
    is_teacher = models.BooleanField(default=False)
    is_student = models.BooleanField(default=False)
    is_parent = models.BooleanField(default=False)
    def __str__(self):
        return self.username

class Teacher(models.Model):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE)
    id_document = models.FileField(upload_to='ids/')
    phone_number = models.CharField(max_length=15)
    address = models.CharField(max_length=255)
    is_verified = models.BooleanField(default=False)
    
    def __str__(self):
        return self.user.username

class Parent(models.Model):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE)
    phone_number = models.CharField(max_length=15)
    id_document = models.FileField(upload_to='ids/')
    is_verified = models.BooleanField(default=False)
    def __str__(self):
        return self.user.username

class Student(models.Model):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE)
    address = models.CharField(max_length=255)
    birthday = models.DateField(null=True, blank=True)
    def __str__(self):
        return self.user.username
    

class Classes(models.Model):
    name = models.CharField(max_length=100)
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='classes')
    students = models.ManyToManyField(Student, related_name='classes', blank=True)
    
    def __str__(self):
        return self.name
    


class Subject(models.Model):
    name = models.CharField(max_length=100)
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='subjects')
    classname = models.ForeignKey(Classes, on_delete=models.CASCADE, related_name='subjects')
    description = models.TextField(blank=True, null=True)
    def __str__(self):
        return self.name


class Topic(models.Model):
    name = models.CharField(max_length=100)
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE, related_name='topics')
    description = models.TextField(blank=True, null=True)
    
    def __str__(self):
        return self.name


class Assignment(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    due_date = models.DateTimeField()
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE, related_name='assignments')
    created_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='created_assignments')
    
    def __str__(self):
        return self.title


class Submission(models.Model):
    assignment = models.ForeignKey(Assignment, on_delete=models.CASCADE, related_name='submissions')
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='submissions')
    submitted_at = models.DateTimeField(auto_now_add=True)
    content = models.TextField()
    submitted_file = models.FileField(upload_to='submissions/')
    grade = models.FloatField(null=True, blank=True)
    
    def __str__(self):
        return f"{self.assignment.title} - {self.student.user.username}"


class Examination(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    subject = models.ForeignKey(Subject, on_delete=models.CASCADE, related_name='exams')
    created_by = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='created_exams')
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    
    def __str__(self):
        return self.title
    

class ExamSubmission(models.Model):
    examination = models.ForeignKey(Examination, on_delete=models.CASCADE, related_name='exam_submissions')
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='exam_submissions')
    submitted_at = models.DateTimeField(auto_now_add=True)
    answers = models.JSONField()  # Assuming answers are stored in JSON format
    score = models.FloatField(null=True, blank=True)
    
    def __str__(self):
        return f"{self.examination.title} - {self.student.user.username}"
    


