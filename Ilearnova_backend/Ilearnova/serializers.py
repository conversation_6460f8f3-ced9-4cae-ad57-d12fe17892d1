from rest_framework import serializers
from .models import  *
from django.conf import settings
from django.core.mail import send_mail

from django.contrib.auth import get_user_model
User = get_user_model()





class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'bio', 'profile_pic', 'password')
        extra_kwargs = {
            'password': {'write_only': True}
        }
        # read_only_fields = ('username',)

class TeacherRegisterSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    id_document = serializers.FileField()
    phone_number = serializers.CharField()
    address = serializers.CharField()

    class Meta:
        model = Teacher
        fields = ['user', 'id_document', 'phone_number', 'address']

    def create(self, validated_data):
        user_data = validated_data.pop('user')
        password = user_data.pop('password')

        user = User.objects.create_user(**user_data, is_teacher=True)
        user.set_password(password)
        user.save()
        teacher = Teacher.objects.create(user=user, **validated_data)
        from_email = settings.DEFAULT_FROM_EMAIL

        from django.urls import reverse
        verification_link = f"http://localhost:8000/{reverse('verify-user', args=['teacher', user.username])}"
        send_mail('Teacher Verification Required', f'Please verify {user.username} by clicking this link: {verification_link}', from_email  , [settings.ADMIN_EMAIL])
        return teacher

class ParentRegisterSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    phone_number = serializers.CharField()
    id_document = serializers.FileField()

    class Meta:
        model = Parent
        fields = ['user', 'phone_number', 'id_document']

    def create(self, validated_data):
        user_data = validated_data.pop('user')
        password = user_data.pop('password')
        user = User.objects.create_user(**user_data, is_parent=True)
        user.set_password(password)
        user.save()
        parent = Parent.objects.create(user=user, **validated_data)
        from django.core.mail import send_mail
        from django.urls import reverse
        verification_link = f"http://localhost:8000/{reverse('verify-user', args=['parent', user.username])}"
        send_mail('Parent Verification Required', f'Please verify {user.username} by clicking this link: {verification_link}', '<EMAIL>', [settings.ADMIN_EMAIL])
        return parent

class StudentRegisterSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    address = serializers.CharField()
    birthday = serializers.DateField()

    class Meta:
        model = Student
        fields = ['user', 'address', 'birthday']

    def create(self, validated_data):
        user_data = validated_data.pop('user')
        password = user_data.pop('password')
        user = User.objects.create_user(**user_data, is_student=True)
        user.set_password(password)
        user.save()
        student = Student.objects.create(user=user, **validated_data)
        return student

class ProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = Student
        fields = ['user', 'address', 'birthday']

    def update(self, instance, validated_data):
        user_data = validated_data.pop('user', None)

        # Update student fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update user fields if provided
        if user_data:
            user = instance.user
            for attr, value in user_data.items():
                setattr(user, attr, value)
            user.save()

        return instance


class UserReadSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('first_name', 'last_name', )


class TeacherReadSerializer(serializers.ModelSerializer):
    user = UserReadSerializer()
    # id_document = serializers.FileField()
    # phone_number = serializers.CharField()
    # address = serializers.CharField()

    class Meta:
        model = Teacher
        fields = ['user',]


class StudentReadSerializer(serializers.ModelSerializer):
    user = UserReadSerializer()
    # id_document = serializers.FileField()
    # phone_number = serializers.CharField()
    # address = serializers.CharField()

    class Meta:
        model = Student
        fields = ['user',]



class ClassesSerialzer(serializers.ModelSerializer):
    teacher = TeacherReadSerializer(read_only=True)
    # students = StudentReadSerializer(many=True, read_only=True)

    class Meta:
        model = Classes
        fields = ['id', 'name', 'teacher',]


class ClassesReadSerializer(serializers.ModelSerializer):
    teacher = TeacherReadSerializer(read_only=True)
    # students = StudentReadSerializer(many=True, read_only=True)
    

    class Meta:
        model = Classes
        fields = ['id', 'name', 'teacher',]


class SubjectSerializer(serializers.ModelSerializer):
    classname = ClassesReadSerializer(read_only=True)
    

    class Meta:
        model = Subject
        fields = ['id', 'name', 'description', 'classname']
    
    

class TopicSerializer(serializers.ModelSerializer):
    subject = SubjectSerializer(read_only=True)

    class Meta:
        model = Topic
        fields = ['id', 'name', 'description', 'subject']



class AssignmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Assignment
        fields = ['id', 'title', 'description', 'due_date',]

  

# serializers.py
class AssignmentSubmissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Submission
        fields = ['id', 'assignment', 'submitted_file', 'submitted_at', 'grade', 'content']
        read_only_fields = ['submitted_at', 'grade', 'feedback', 'assignment']

    def create(self, validated_data):
        return Submission.objects.create(**validated_data)
